package com.weaver.seconddev.zyhlw.util;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * QueryWrapper SQL工具类
 * 支持完整的SQL提取，包括SELECT、ORDER BY、GROUP BY等子句
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Slf4j
@Component
public class QueryWrapperSqlUtils {

    /**
     * 提取完整的SQL信息（包括SELECT、ORDER BY等）
     */
    public static <T> CompleteSqlResult extractCompleteSql(QueryWrapper<T> queryWrapper) {
        String tableName = getTableName(queryWrapper);

        // 获取各个SQL片段
        String selectSql = getSelectSql(queryWrapper);
        String fromSql = "FROM " + tableName;
        String whereSql = getWhereSql(queryWrapper);

        // 组装完整SQL
        StringBuilder completeSql = new StringBuilder();
        completeSql.append(selectSql).append(" ");
        completeSql.append(fromSql);

        if (StringUtils.isNotEmpty(whereSql)) {
            completeSql.append(" WHERE ").append(whereSql);
        }

        // 获取参数
        Map<String, Object> paramMap = queryWrapper.getParamNameValuePairs();
        List<Object> params = extractParamsInOrder(completeSql.toString(), paramMap);

        // 替换占位符
        String finalSql = completeSql.toString()
                .replaceAll("#\\{ew\\.paramNameValuePairs\\.[^}]+}", "?");

        return new CompleteSqlResult(finalSql, params, tableName);
    }

    /**
     * 提取基础SQL信息（兼容原有方法）
     */
    public static <T> SqlResult extractSql(QueryWrapper<T> queryWrapper) {
        CompleteSqlResult completeSqlResult = extractCompleteSql(queryWrapper);
        return new SqlResult(completeSqlResult.getSql(), completeSqlResult.getParams());
    }

    /**
     * 从QueryWrapper提取SQL和参数（指定表名）
     */
    public static SqlResult extractSql(QueryWrapper<?> queryWrapper, String tableName) {
        String sqlSegment = queryWrapper.getSqlSegment();
        Map<String, Object> paramMap = queryWrapper.getParamNameValuePairs();

        // 构建完整SQL
        StringBuilder sql = new StringBuilder("SELECT * FROM ").append(tableName);
        if (StringUtils.isNotEmpty(sqlSegment)) {
            sql.append(" WHERE ").append(sqlSegment);
        }

        // 替换参数占位符为?
        String finalSql = sql.toString();
        List<Object> params = new ArrayList<>();

        // 按照参数在SQL中出现的顺序提取参数值
        Pattern pattern = Pattern.compile("#\\{ew\\.paramNameValuePairs\\.(MPGENVAL\\d+)\\}");
        Matcher matcher = pattern.matcher(finalSql);

        while (matcher.find()) {
            String paramKey = matcher.group(1);
            params.add(paramMap.get(paramKey));
        }

        // 替换占位符为?
        finalSql = finalSql.replaceAll("#\\{ew\\.paramNameValuePairs\\.[^}]+\\}", "?");

        return new SqlResult(finalSql, params);
    }

    /**
     * 获取可直接执行的SQL（自动获取表名）
     */
    public static <T> String getExecutableSql(QueryWrapper<T> queryWrapper) {
        SqlResult sqlResult = extractSql(queryWrapper);
        return sqlResult.getExecutableSql();
    }

    /**
     * 获取可直接执行的SQL（指定表名）
     */
    public static String getExecutableSql(QueryWrapper<?> queryWrapper, String tableName) {
        SqlResult sqlResult = extractSql(queryWrapper, tableName);
        return sqlResult.getExecutableSql();
    }

    /**
     * 获取SELECT子句
     */
    private static String getSelectSql(QueryWrapper<?> queryWrapper) {
        String sqlSelect = queryWrapper.getSqlSelect();
        return StringUtils.isNotEmpty(sqlSelect) ? String.format("SELECT %s", sqlSelect) : "SELECT *";
    }

    /**
     * 获取WHERE子句
     */
    private static String getWhereSql(QueryWrapper<?> queryWrapper) {
        return queryWrapper.getSqlSegment();
    }

    /**
     * 按顺序提取参数
     */
    private static List<Object> extractParamsInOrder(String sql, Map<String, Object> paramMap) {
        List<Object> params = new ArrayList<>();
        Pattern pattern = Pattern.compile("#\\{ew\\.paramNameValuePairs\\.(MPGENVAL\\d+)}");
        Matcher matcher = pattern.matcher(sql);

        while (matcher.find()) {
            String paramKey = matcher.group(1);
            params.add(paramMap.get(paramKey));
        }

        return params;
    }

    /**
     * 从QueryWrapper中获取实体类的表名
     */
    @SuppressWarnings("unchecked")
    private static <T> String getTableName(QueryWrapper<T> queryWrapper) {
        try {
            // 通过反射获取QueryWrapper中的entityClass
            Field entityClassField = AbstractWrapper.class.getDeclaredField("entityClass");
            entityClassField.setAccessible(true);
            Class<T> entityClass = (Class<T>) entityClassField.get(queryWrapper);

            if (entityClass != null) {
                return getTableNameFromEntity(entityClass);
            }

            // 如果无法获取entityClass，尝试其他方式
            return getTableNameFromWrapper(queryWrapper);

        } catch (Exception e) {
            throw new RuntimeException("无法获取表名，请手动指定表名", e);
        }
    }

    /**
     * 从实体类获取表名
     */
    private static <T> String getTableNameFromEntity(Class<T> entityClass) {
        // 检查@TableName注解
        TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
        if (tableNameAnnotation != null && StringUtils.isNotEmpty(tableNameAnnotation.value())) {
            return tableNameAnnotation.value();
        }

        // 如果没有@TableName注解，使用类名转换为下划线格式
        return camelToUnderscore(entityClass.getSimpleName());
    }

    /**
     * 从QueryWrapper中尝试获取表名（备用方案）
     */
    private static String getTableNameFromWrapper(QueryWrapper<?> queryWrapper) {
        try {
            // 尝试通过getTargetSql()方法获取
            String targetSql = queryWrapper.getTargetSql();
            if (StringUtils.isNotEmpty(targetSql)) {
                // 从SQL中提取表名（简单实现）
                Pattern pattern = Pattern.compile("FROM\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(targetSql);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式
        }

        throw new RuntimeException("无法自动获取表名，请手动指定");
    }

    /**
     * 驼峰转下划线
     */
    private static String camelToUnderscore(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 格式化参数值
     */
    private static String formatParamValue(Object param) {
        if (param == null) {
            return "NULL";
        }
        if (param instanceof String) {
            return "'" + param.toString().replace("'", "''") + "'";
        }
        if (param instanceof Date) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return "'" + sdf.format((Date) param) + "'";
        }
        if (param instanceof LocalDateTime) {
            return "'" + ((LocalDateTime) param).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "'";
        }
        return param.toString();
    }

    /**
     * 完整SQL结果类
     */
    @Data
    @AllArgsConstructor
    public static class CompleteSqlResult {
        private String sql;
        private List<Object> params;
        private String tableName;

        /**
         * 获取可执行的SQL（参数已替换）
         */
        public String getExecutableSql() {
            String executableSql = sql;
            for (Object param : params) {
                String paramStr = formatParamValue(param);
                executableSql = executableSql.replaceFirst("\\?", paramStr);
            }
            return executableSql;
        }
    }

    /**
     * 基础SQL结果类（兼容性）
     */
    @Data
    @AllArgsConstructor
    public static class SqlResult {
        private String sql;
        private List<Object> params;

        /**
         * 获取可执行的SQL（参数已替换）
         */
        public String getExecutableSql() {
            String executableSql = sql;
            for (Object param : params) {
                String paramStr = formatParamValue(param);
                executableSql = executableSql.replaceFirst("\\?", paramStr);
            }
            return executableSql;
        }
    }
}
